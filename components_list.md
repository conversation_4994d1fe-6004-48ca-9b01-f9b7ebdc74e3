# Component Shopping List - Staircase Automation

## Essential Components

### Microcontroller
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| Arduino Nano | ATmega328P, USB-C preferred | 1 | $15-25 |

### Sensors
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| PIR Motion Sensor | HC-SR501, 3.3V-5V, adjustable sensitivity | 2 | $5-8 each |

### LED Strip
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| ARGB LED Strip | WS2812B, 12V, 60 LEDs/m, IP65 rated | 7 meters | $50-80 |
| LED Strip Connectors | For WS2812B, waterproof | 2-4 | $10-15 |

### Power Supply
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| 12V Power Supply | 12V DC, 10-15A, switching type | 1 | $30-50 |
| Buck Converter | 12V to 5V, 3A minimum, adjustable | 1 | $8-12 |

### Electronics
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| Logic Level Shifter | 3.3V/5V to 5V, bidirectional | 1 | $3-5 |
| Resistors | 470Ω (LED data), 10kΩ (pull-up) | 5 each | $2-3 |
| Capacitors | 1000µF 16V electrolytic | 2 | $3-5 |
| Breadboard/PCB | For prototyping/permanent install | 1 | $5-15 |

### Cables and Connectors
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| Shielded Cable | Cat5e or Cat6, 4-pair | 10 meters | $15-25 |
| Power Wire | 18 AWG, red/black pair | 10 meters | $10-15 |
| Dupont Connectors | Male/Female, various | 20 | $5-8 |
| Terminal Blocks | Screw type, 2-3 position | 5 | $8-12 |

## Installation Materials

### Mounting Hardware
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| Enclosure Box | IP65, for Arduino and electronics | 1 | $15-25 |
| PIR Sensor Mounts | Adjustable angle brackets | 2 | $10-15 |
| Cable Conduit | PVC or aluminum, 20mm diameter | 8 meters | $20-30 |
| Mounting Screws | Various sizes for installation | 1 set | $5-10 |

### Tools (if needed)
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| Soldering Iron | 40W, temperature controlled | 1 | $25-40 |
| Wire Strippers | Multi-gauge | 1 | $15-25 |
| Multimeter | Basic DC voltage/current | 1 | $20-35 |
| Heat Shrink Tubing | Various sizes | 1 set | $10-15 |

## Optional Enhancements

### Advanced Features
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| WiFi Module | ESP8266 or ESP32 for remote control | 1 | $10-20 |
| Temperature Sensor | DS18B20 for thermal monitoring | 1 | $3-5 |
| Light Sensor | LDR for automatic brightness | 1 | $2-3 |
| Real-time Clock | DS3231 for scheduling | 1 | $5-8 |

### Protection Components
| Component | Specification | Quantity | Estimated Price |
|-----------|---------------|----------|-----------------|
| Fuse Holder | 15A automotive type | 1 | $3-5 |
| Fuses | 15A blade type | 3 | $5-8 |
| TVS Diodes | For surge protection | 2 | $3-5 |
| Ferrite Cores | For EMI suppression | 4 | $5-10 |

## Total Estimated Cost

### Basic System
- **Essential Components**: $180-280
- **Installation Materials**: $85-125
- **Tools** (if needed): $70-115
- **Total Basic System**: $265-405

### Enhanced System (with optional features)
- **Basic System**: $265-405
- **Advanced Features**: $20-36
- **Protection Components**: $16-28
- **Total Enhanced System**: $301-469

## Recommended Suppliers

### Online Retailers
1. **Amazon** - Good for complete kits and fast shipping
2. **AliExpress** - Lowest prices, longer shipping times
3. **Adafruit** - High quality, good documentation
4. **SparkFun** - Educational resources, reliable components
5. **Digi-Key/Mouser** - Professional grade, exact specifications

### Local Options
1. **Electronics stores** - Immediate availability, higher prices
2. **Hardware stores** - Installation materials, basic tools
3. **Electrical supply** - Power supplies, conduit, wire

## Quality Considerations

### Critical Components (Don't Compromise)
- **Power Supply**: Use switching type, proper current rating
- **LED Strip**: Ensure 12V compatibility and good color consistency
- **Buck Converter**: Stable output, adequate current capacity
- **Cables**: Proper gauge for current, shielded for signals

### Budget-Friendly Options
- **Arduino**: Compatible clones work fine for this application
- **PIR Sensors**: Generic HC-SR501 modules are adequate
- **Resistors/Capacitors**: Basic components, no need for precision
- **Enclosures**: Plastic project boxes work well

## Purchasing Tips

1. **Buy extra components**: 10-20% extra for replacements/mistakes
2. **Check compatibility**: Verify voltage levels and pin configurations
3. **Read reviews**: Especially for power supplies and LED strips
4. **Consider kits**: Some suppliers offer complete automation kits
5. **Plan for expansion**: Buy slightly larger power supply for future additions

## Delivery Timeline

### Standard Shipping
- **Local suppliers**: 1-3 days
- **Amazon Prime**: 1-2 days
- **Standard online**: 5-10 days
- **International**: 2-4 weeks

### Project Timeline
1. **Week 1**: Order components, prepare installation area
2. **Week 2**: Assemble and test electronics on breadboard
3. **Week 3**: Install mounting hardware and run cables
4. **Week 4**: Final installation and testing

## Warranty and Support

### Component Warranties
- **Arduino**: Usually 1 year
- **Power supplies**: 1-2 years typical
- **LED strips**: 6 months to 1 year
- **Sensors**: 30 days to 6 months

### Technical Support
- **Arduino community**: Extensive online forums
- **FastLED library**: Active GitHub community
- **Supplier support**: Varies by vendor
- **Documentation**: Comprehensive guides included with this project
