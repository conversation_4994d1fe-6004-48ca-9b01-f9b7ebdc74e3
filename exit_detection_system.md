# Exit Detection System

## Overview

The enhanced staircase automation system now includes intelligent exit detection that automatically turns off the lights when a person leaves the staircase, rather than waiting for the timeout period. This makes the system more responsive, energy-efficient, and provides a better user experience.

## How Exit Detection Works

### Basic Principle
The system tracks when a person enters the staircase and monitors for their exit based on the direction they were traveling:

- **Upward Movement**: Monitors top sensor for person leaving upstairs
- **Downward Movement**: Monitors bottom sensor for person leaving downstairs
- **Direction Change**: Detects if person returns and exits in opposite direction

### Detection Algorithm

```cpp
// Key parameters
unsigned long exitDetectionWindow = 8000;  // 8 seconds to detect exit
bool personOnStairs = false;               // Track occupancy
bool waitingForExit = false;               // Active exit monitoring

// Detection logic:
1. Person enters → Set personOnStairs = true
2. Animation completes → Set waitingForExit = true
3. Monitor appropriate sensor for exit
4. Sensor clears → Immediate light turn-off
```

## Exit Detection Scenarios

### 1. **Normal Exit (Primary Mode)**
**Most Common Scenario**

```
Timeline - Upward Movement:
T+0ms:     Bottom PIR triggers → Person enters
T+1500ms:  Top PIR triggers → Direction confirmed UP
T+2000ms:  Animation starts bottom→top
T+4000ms:  Animation completes → Start exit monitoring
T+6000ms:  Top PIR goes LOW → Person exited upstairs
T+6001ms:  Lights turn off immediately

Timeline - Downward Movement:
T+0ms:     Top PIR triggers → Person enters  
T+1200ms:  Bottom PIR triggers → Direction confirmed DOWN
T+1500ms:  Animation starts top→bottom
T+3500ms:  Animation completes → Start exit monitoring
T+5500ms:  Bottom PIR goes LOW → Person exited downstairs
T+5501ms:  Lights turn off immediately
```

### 2. **Direction Change Exit**
**Person Changes Mind and Returns**

```
Timeline - Changed Direction:
T+0ms:     Bottom PIR triggers → Person enters going UP
T+4000ms:  Animation completes → Monitoring top sensor
T+6000ms:  Bottom PIR goes LOW → Person returned and exited DOWN
T+6001ms:  Lights turn off immediately
```

### 3. **Exit Timeout (Fallback)**
**Safety Net for Edge Cases**

```
Timeline - Timeout Fallback:
T+0ms:     Person enters and animation completes
T+8000ms:  Exit detection window expires
T+8001ms:  Assume person left → Lights turn off
```

## Configuration Parameters

### **Exit Detection Settings**
```cpp
// Core exit detection parameters
unsigned long exitDetectionWindow = 8000;  // 8 seconds to detect exit
bool personOnStairs = false;               // Occupancy tracking
bool waitingForExit = false;               // Exit monitoring flag

// Timing parameters
unsigned long exitCheckDelay = 1000;       // 1 second before checking exit
unsigned long directionChangeDelay = 2000; // 2 seconds for direction change
```

### **Sensitivity Tuning**
```cpp
// For quick walkers (reduce window)
unsigned long exitDetectionWindow = 6000;  // 6 seconds

// For slow walkers (increase window)  
unsigned long exitDetectionWindow = 12000; // 12 seconds

// For very sensitive PIRs (increase delay)
unsigned long exitCheckDelay = 2000;       // 2 seconds before checking
```

## Serial Monitor Output

### **Normal Exit Detection**
```
Person entered staircase - tracking for exit
Upward animation complete - waiting for exit detection
Exit detected: Person left stairs going UP
Person exited staircase - turning off lights immediately
```

### **Direction Change Exit**
```
Person entered staircase - tracking for exit
Downward animation complete - waiting for exit detection
Exit detected: Person returned and left stairs going UP
Person exited staircase - turning off lights immediately
```

### **Timeout Fallback**
```
Person entered staircase - tracking for exit
Upward animation complete - waiting for exit detection
Exit timeout reached - assuming person left stairs
Person exited staircase - turning off lights immediately
```

## Benefits of Exit Detection

### **1. Energy Efficiency**
- **Immediate Turn-off**: No waiting for 30-second timeout
- **Reduced Runtime**: Lights on only while person is actually on stairs
- **Smart Monitoring**: Only active monitoring when someone is present

### **2. Better User Experience**
- **Responsive**: Lights turn off as soon as person leaves
- **Natural Feel**: System responds to actual behavior
- **No Wasted Light**: Eliminates unnecessary illumination

### **3. System Intelligence**
- **Occupancy Tracking**: Knows when stairs are occupied
- **Direction Awareness**: Monitors correct exit point
- **Adaptive Behavior**: Handles direction changes gracefully

## Exit Detection Logic Flow

### **Entry Phase**
```cpp
void triggerAnimation(bool upward) {
  // Start animation
  personOnStairs = true;        // Mark stairs as occupied
  entryTime = millis();         // Record entry time
  waitingForExit = false;       // Not yet waiting for exit
}
```

### **Animation Complete Phase**
```cpp
void animationComplete() {
  waitingForExit = true;        // Start exit monitoring
  lastExitCheck = millis();     // Record when monitoring started
}
```

### **Exit Monitoring Phase**
```cpp
void handleExitDetection() {
  if (directionUp) {
    // Monitor top sensor for exit
    if (!pirTopState && timeDelay > 1000) {
      exitDetected = true;
    }
  } else {
    // Monitor bottom sensor for exit
    if (!pirBottomState && timeDelay > 1000) {
      exitDetected = true;
    }
  }
}
```

## Troubleshooting Exit Detection

### **Problem: Lights Turn Off Too Quickly**

**Symptoms:**
- Lights turn off while person still on stairs
- "Exit detected" messages when person hasn't left

**Solutions:**
```cpp
// Increase exit check delay
unsigned long exitCheckDelay = 2000;      // From 1000ms to 2000ms

// Increase direction change delay
unsigned long directionChangeDelay = 3000; // From 2000ms to 3000ms

// Increase exit detection window
unsigned long exitDetectionWindow = 12000; // From 8000ms to 12000ms
```

### **Problem: Lights Don't Turn Off When Person Leaves**

**Symptoms:**
- Person clearly left but lights stay on
- Eventually timeout after 30 seconds
- No "Exit detected" messages

**Solutions:**
```cpp
// Reduce exit check delay
unsigned long exitCheckDelay = 500;       // From 1000ms to 500ms

// Check PIR sensor sensitivity
// Ensure sensors can detect person leaving area

// Add debug output
Serial.print("Monitoring exit - Top PIR: ");
Serial.print(pirTopState);
Serial.print(", Bottom PIR: ");
Serial.println(pirBottomState);
```

### **Problem: False Exit Detection**

**Symptoms:**
- Lights turn off when person stops on stairs
- Exit detected during normal stair usage

**Solutions:**
```cpp
// Increase minimum monitoring time
if ((currentTime - lastExitCheck) > 3000) {  // From 1000ms to 3000ms
  // Check for exit
}

// Add movement confirmation
bool confirmExit() {
  // Check sensor state multiple times
  delay(500);
  return !pirTopState;  // Confirm still clear
}
```

## Advanced Exit Detection Features

### **Multi-Person Support**
Handle multiple people on stairs simultaneously:

```cpp
int peopleCount = 0;           // Track number of people
bool multiPersonMode = false;  // Special handling mode

void handleMultiPerson() {
  if (peopleCount > 1) {
    multiPersonMode = true;
    // Extend exit detection window
    exitDetectionWindow = 15000;
  }
}
```

### **Adaptive Exit Windows**
Automatically adjust based on detected patterns:

```cpp
void adaptiveExitTiming() {
  // Learn from successful exits
  if (exitDetected) {
    unsigned long actualExitTime = millis() - entryTime;
    exitDetectionWindow = actualExitTime * 1.5;  // Add 50% margin
  }
}
```

### **Smart Timeout Adjustment**
Different timeouts based on direction and time of day:

```cpp
unsigned long getExitWindow() {
  if (directionUp) {
    return exitDetectionWindow * 1.5;  // Longer for upward (going to bed)
  }
  return exitDetectionWindow;
}
```

## Installation Considerations

### **PIR Sensor Placement for Exit Detection**
Optimal placement for reliable exit detection:

1. **Coverage Area**: Ensure sensors cover exit paths completely
2. **Detection Range**: Adjust range to detect person leaving area
3. **Mounting Height**: Position to avoid false triggers from pets
4. **Angle Adjustment**: Slight outward angle to catch exit movement

### **Testing Exit Detection**
Comprehensive testing procedure:

1. **Walk up stairs normally** → Verify exit detection at top
2. **Walk down stairs normally** → Verify exit detection at bottom
3. **Change direction mid-stair** → Test direction change handling
4. **Stop on stairs briefly** → Ensure no false exit detection
5. **Multiple people test** → Verify multi-person scenarios

### **Performance Optimization**
```cpp
// For very long staircases
unsigned long exitDetectionWindow = 12000;  // 12 seconds

// For short staircases  
unsigned long exitDetectionWindow = 5000;   // 5 seconds

// For high-traffic areas
unsigned long exitCheckDelay = 500;         // Faster exit checking
```

## Energy Savings Analysis

### **Before Exit Detection**
- **Average on-time**: 30 seconds (timeout period)
- **Daily activations**: 50 times
- **Total daily runtime**: 25 minutes
- **Energy consumption**: High due to full timeout periods

### **After Exit Detection**
- **Average on-time**: 8 seconds (actual usage time)
- **Daily activations**: 50 times  
- **Total daily runtime**: 6.7 minutes
- **Energy savings**: 73% reduction in runtime

### **Real-World Benefits**
- **Immediate response**: Lights off within 1 second of exit
- **No wasted energy**: Only illuminated when needed
- **Extended LED life**: Reduced operating hours
- **Better user experience**: Natural, responsive behavior

This exit detection system transforms your staircase automation from a simple timer-based system into an intelligent occupancy-aware lighting solution that responds naturally to human behavior.
