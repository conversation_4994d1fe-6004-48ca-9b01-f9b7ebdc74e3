# Staircase Automation Installation Guide

## Prerequisites

### Required Libraries
Install these libraries in Arduino IDE:
```
1. FastLED library (by <PERSON>)
   - Go to Tools → Manage Libraries
   - Search for "FastLED"
   - Install latest version
```

### Tools Required
- Soldering iron and solder
- Wire strippers
- Multimeter
- Drill and bits
- Cable management supplies
- Heat shrink tubing

## Step-by-Step Installation

### Step 1: Prepare the Hardware

1. **Test all components individually**
   - Verify Arduino Nano functionality
   - Test PIR sensors with simple sketch
   - Test LED strip with basic FastLED example

2. **Calculate exact LED count**
   - Measure your 7m LED strip
   - Count LEDs per meter (usually 30, 60, or 144)
   - Update `NUM_LEDS` in the code accordingly

### Step 2: Power System Setup

1. **Install 12V Power Supply**
   - Choose location near electrical panel
   - Ensure adequate ventilation
   - Install appropriate circuit breaker/fuse

2. **Set up Buck Converter**
   - Mount near the top PIR sensor location
   - Adjust output to exactly 5V using multimeter
   - Test with load before connecting PIR

### Step 3: PIR Sensor Installation

#### Bottom PIR Sensor (Near Arduino)
1. Mount at bottom of staircase
2. Height: 1-2 meters from ground
3. Angle: Slightly downward to detect foot traffic
4. Connect directly to Arduino with short wires

#### Top PIR Sensor (7m away)
1. Mount at top of staircase
2. Same height and angle as bottom sensor
3. Connect to buck converter for power
4. Run signal cable back to Arduino

### Step 4: Cable Installation

#### Signal Cable (7m)
```bash
# Recommended cable routing:
1. Use cable conduit or trunking
2. Avoid running parallel to AC power lines
3. Secure every 50cm to prevent sagging
4. Leave service loops at both ends
```

#### Cable Connections
```
At PIR Top End:
- Red wire    → Buck converter 5V+
- Black wire  → Buck converter GND
- White wire  → PIR signal output

At Arduino End:
- Red wire    → Not connected (PIR powered remotely)
- Black wire  → Arduino GND
- White wire  → Arduino D3 (with 10kΩ pull-up to 5V)
```

### Step 5: LED Strip Installation

1. **Plan the route**
   - Measure exact path along stairs
   - Plan for corners and bends
   - Identify mounting points

2. **Install LED strip**
   - Clean mounting surface
   - Remove adhesive backing gradually
   - Press firmly for good adhesion
   - Use additional mounting clips if needed

3. **Power injection** (for 7m strip)
   - Connect power at beginning
   - Consider mid-point power injection for better performance
   - Use appropriate wire gauge (18 AWG minimum)

### Step 6: Arduino Programming

1. **Upload the code**
   ```bash
   1. Connect Arduino to computer via USB
   2. Select correct board: Tools → Board → Arduino Nano
   3. Select correct processor: Tools → Processor → ATmega328P
   4. Select correct port: Tools → Port → (your COM port)
   5. Upload the sketch
   ```

2. **Initial configuration**
   - Open Serial Monitor (9600 baud)
   - Verify startup messages
   - Test each PIR sensor individually

### Step 7: System Testing

#### Basic Function Test
1. **Power on sequence**
   - Connect 12V power supply
   - Arduino should show startup animation
   - Serial monitor should show "System Ready!"

2. **PIR sensor test**
   - Walk past bottom sensor
   - Verify "Motion detected at BOTTOM" message
   - Check LED animation starts from bottom
   - Repeat for top sensor

#### Performance Optimization

1. **Adjust PIR sensitivity**
   - Use potentiometers on PIR sensors
   - Test different sensitivity levels
   - Find balance between responsiveness and false triggers

2. **Fine-tune timing**
   ```cpp
   // Adjust these values in the code:
   #define MOTION_TIMEOUT 30000    // Time before auto-off
   #define DEBOUNCE_TIME 500       // PIR debounce time
   #define ANIMATION_SPEED 50      // Animation speed
   ```

## Configuration Options

### Customizable Parameters

```cpp
// In staircase_automation.ino, modify these values:

// LED Configuration
#define NUM_LEDS 420            // Your actual LED count
#define BRIGHTNESS 150          // 0-255, adjust for desired brightness

// Timing Configuration  
#define MOTION_TIMEOUT 30000    // Auto-off time (milliseconds)
#define DEBOUNCE_TIME 500       // PIR debounce (milliseconds)
#define ANIMATION_SPEED 50      // Animation delay (milliseconds)

// Animation Configuration
#define FADE_STEPS 20           // Number of animation steps
```

### Color Customization

Replace `CRGB::White` in the animation functions with:
```cpp
CRGB::Warm_White    // Warm white
CRGB::Blue          // Blue
CRGB::Red           // Red
CRGB(255, 200, 100) // Custom RGB values
```

## Troubleshooting

### Common Issues

1. **LEDs don't light up**
   - Check 12V power supply
   - Verify LED strip polarity
   - Test with multimeter

2. **PIR sensors not responding**
   - Check 5V power to sensors
   - Verify signal connections
   - Adjust sensitivity potentiometers

3. **Intermittent operation**
   - Check all ground connections
   - Verify cable integrity
   - Add pull-up resistor to signal lines

4. **False triggers**
   - Adjust PIR sensitivity
   - Increase debounce time
   - Check for EMI sources

### Serial Monitor Debug Messages

```
Normal operation:
"System Ready!"
"Motion detected at BOTTOM"
"Starting upward animation"
"Upward animation complete"
"Timeout reached, turning off LEDs"

Error indicators:
- No startup message: Arduino not running
- No motion messages: PIR sensor issues
- No LED response: Power or data line issues
```

## Maintenance

### Regular Checks
- Clean PIR sensor lenses monthly
- Check cable connections quarterly
- Verify LED strip adhesion
- Monitor power supply temperature

### Seasonal Adjustments
- Adjust PIR sensitivity for temperature changes
- Update timeout values for usage patterns
- Check for moisture in outdoor installations
