/*
 * Staircase Automation System
 * Author: Arduino Project
 * Description: Stable staircase lighting with two PIR sensors and ARGB LED strip
 * 
 * Hardware:
 * - Arduino Nano
 * - 2x PIR Motion Sensors (HC-SR501)
 * - 12V ARGB LED Strip (WS2812B compatible)
 * - Buck converter (12V to 5V)
 * - Level shifter for LED data signal
 */

#include <FastLED.h>

// Pin definitions
#define PIR_BOTTOM_PIN 2        // PIR sensor at bottom of stairs
#define PIR_TOP_PIN 3           // PIR sensor at top of stairs
#define LED_DATA_PIN 6          // Data pin for LED strip
#define LED_POWER_PIN 7         // Optional power control for LEDs

// LED strip configuration
#define NUM_LEDS 420            // Adjust based on your 7m strip (60 LEDs/m typical)
#define LED_TYPE WS2812B
#define COLOR_ORDER GRB
#define BRIGHTNESS 150          // Adjust brightness (0-255)

// Timing constants
#define MOTION_TIMEOUT 30000    // 30 seconds before turning off
#define DEBOUNCE_TIME 500       // 500ms debounce for PIR sensors
#define ANIMATION_SPEED 50      // Speed of LED animation (lower = faster)
#define FADE_STEPS 20           // Number of steps for fade in/out

// LED array
CRGB leds[NUM_LEDS];

// State variables
bool motionDetected = false;
bool ledsOn = false;
unsigned long lastMotionTime = 0;
unsigned long lastPirBottomTime = 0;
unsigned long lastPirTopTime = 0;
bool pirBottomState = false;
bool pirTopState = false;

// Animation variables
int currentStep = 0;
bool animatingUp = false;
bool animatingDown = false;
unsigned long lastAnimationTime = 0;

void setup() {
  Serial.begin(9600);
  Serial.println("Staircase Automation System Starting...");
  
  // Initialize pins
  pinMode(PIR_BOTTOM_PIN, INPUT);
  pinMode(PIR_TOP_PIN, INPUT);
  pinMode(LED_POWER_PIN, OUTPUT);
  
  // Initialize LED strip
  FastLED.addLeds<LED_TYPE, LED_DATA_PIN, COLOR_ORDER>(leds, NUM_LEDS);
  FastLED.setBrightness(BRIGHTNESS);
  FastLED.clear();
  FastLED.show();
  
  // Power on LEDs
  digitalWrite(LED_POWER_PIN, HIGH);
  
  // Startup animation
  startupAnimation();
  
  Serial.println("System Ready!");
}

void loop() {
  // Read PIR sensors with debouncing
  readPirSensors();
  
  // Handle motion detection logic
  handleMotionLogic();
  
  // Handle LED animations
  handleAnimations();
  
  // Auto turn off after timeout
  handleTimeout();
  
  delay(10); // Small delay for stability
}

void readPirSensors() {
  unsigned long currentTime = millis();
  
  // Read bottom PIR with debouncing
  bool bottomReading = digitalRead(PIR_BOTTOM_PIN);
  if (bottomReading != pirBottomState && 
      (currentTime - lastPirBottomTime) > DEBOUNCE_TIME) {
    pirBottomState = bottomReading;
    lastPirBottomTime = currentTime;
    
    if (pirBottomState) {
      Serial.println("Motion detected at BOTTOM");
      triggerAnimation(true); // Animate upward
    }
  }
  
  // Read top PIR with debouncing
  bool topReading = digitalRead(PIR_TOP_PIN);
  if (topReading != pirTopState && 
      (currentTime - lastPirTopTime) > DEBOUNCE_TIME) {
    pirTopState = topReading;
    lastPirTopTime = currentTime;
    
    if (pirTopState) {
      Serial.println("Motion detected at TOP");
      triggerAnimation(false); // Animate downward
    }
  }
}

void handleMotionLogic() {
  if (pirBottomState || pirTopState) {
    motionDetected = true;
    lastMotionTime = millis();
  }
}

void triggerAnimation(bool upward) {
  if (!ledsOn) {
    ledsOn = true;
    currentStep = 0;
    animatingUp = upward;
    animatingDown = !upward;
    lastAnimationTime = millis();
    Serial.println(upward ? "Starting upward animation" : "Starting downward animation");
  } else {
    // If already on, just reset the timeout
    lastMotionTime = millis();
  }
}

void handleAnimations() {
  unsigned long currentTime = millis();
  
  if ((animatingUp || animatingDown) && 
      (currentTime - lastAnimationTime) > ANIMATION_SPEED) {
    
    if (animatingUp) {
      animateUpward();
    } else if (animatingDown) {
      animateDownward();
    }
    
    lastAnimationTime = currentTime;
  }
}

void animateUpward() {
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  int endLed = min((currentStep + 1) * ledsPerStep, NUM_LEDS);
  
  // Light up LEDs from bottom to top
  for (int i = currentStep * ledsPerStep; i < endLed; i++) {
    leds[i] = CRGB::White;
  }
  
  FastLED.show();
  currentStep++;
  
  if (currentStep >= FADE_STEPS) {
    animatingUp = false;
    Serial.println("Upward animation complete");
  }
}

void animateDownward() {
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  int startLed = max(NUM_LEDS - (currentStep + 1) * ledsPerStep, 0);
  
  // Light up LEDs from top to bottom
  for (int i = NUM_LEDS - currentStep * ledsPerStep - 1; i >= startLed; i--) {
    leds[i] = CRGB::White;
  }
  
  FastLED.show();
  currentStep++;
  
  if (currentStep >= FADE_STEPS) {
    animatingDown = false;
    Serial.println("Downward animation complete");
  }
}

void handleTimeout() {
  if (motionDetected && ledsOn && 
      (millis() - lastMotionTime) > MOTION_TIMEOUT) {
    
    if (!pirBottomState && !pirTopState) {
      Serial.println("Timeout reached, turning off LEDs");
      fadeOutLeds();
      ledsOn = false;
      motionDetected = false;
      currentStep = 0;
      animatingUp = false;
      animatingDown = false;
    }
  }
}

void fadeOutLeds() {
  for (int brightness = BRIGHTNESS; brightness >= 0; brightness -= 5) {
    FastLED.setBrightness(brightness);
    FastLED.show();
    delay(50);
  }
  
  FastLED.clear();
  FastLED.show();
  FastLED.setBrightness(BRIGHTNESS); // Reset brightness for next time
}

void startupAnimation() {
  Serial.println("Running startup animation...");
  
  // Quick flash to indicate system is ready
  for (int i = 0; i < 3; i++) {
    fill_solid(leds, NUM_LEDS, CRGB::Blue);
    FastLED.show();
    delay(200);
    FastLED.clear();
    FastLED.show();
    delay(200);
  }
}
