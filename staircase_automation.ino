/*
 * Staircase Automation System
 * Author: Arduino Project
 * Description: Stable staircase lighting with two PIR sensors and ARGB LED strip
 * 
 * Hardware:
 * - Arduino Nano
 * - 2x PIR Motion Sensors (HC-SR501)
 * - 12V ARGB LED Strip (WS2812B compatible)
 * - Buck converter (12V to 5V)
 * - Level shifter for LED data signal
 */

#include <FastLED.h>

// Pin definitions
#define PIR_BOTTOM_PIN 2        // PIR sensor at bottom of stairs
#define PIR_TOP_PIN 3           // PIR sensor at top of stairs
#define LED_DATA_PIN 6          // Data pin for LED strip
#define LED_POWER_PIN 7         // Optional power control for LEDs

// LED strip configuration
#define NUM_LEDS 420            // Adjust based on your 7m strip (60 LEDs/m typical)
#define LED_TYPE WS2812B
#define COLOR_ORDER GRB
#define BRIGHTNESS 150          // Adjust brightness (0-255)

// Timing constants
#define MOTION_TIMEOUT 30000    // 30 seconds before turning off
#define DEBOUNCE_TIME 500       // 500ms debounce for PIR sensors
#define ANIMATION_SPEED 50      // Speed of LED animation (lower = faster)
#define FADE_STEPS 20           // Number of steps for fade in/out

// LED array
CRGB leds[NUM_LEDS];

// State variables
bool motionDetected = false;
bool ledsOn = false;
unsigned long lastMotionTime = 0;
unsigned long lastPirBottomTime = 0;
unsigned long lastPirTopTime = 0;
bool pirBottomState = false;
bool pirTopState = false;

// Direction detection variables
unsigned long lastBottomTrigger = 0;
unsigned long lastTopTrigger = 0;
bool directionUp = true;  // true = bottom to top, false = top to bottom
unsigned long directionWindow = 3000;  // 3 seconds to determine direction

// False detection prevention
unsigned long minTriggerInterval = 800;   // Minimum time between valid triggers
unsigned long maxTriggerInterval = 5000;  // Maximum time for valid sequence
int confirmationCount = 0;                // Count confirmations before acting
int requiredConfirmations = 2;            // Number of confirmations needed
unsigned long lastValidDirection = 0;     // Last confirmed direction time
bool directionLocked = false;             // Prevent direction changes during animation

// Sensor validation
unsigned long bottomTriggerHistory[3] = {0, 0, 0};  // Last 3 triggers
unsigned long topTriggerHistory[3] = {0, 0, 0};     // Last 3 triggers
int historyIndex = 0;

// Animation variables
int currentStep = 0;
bool animatingUp = false;
bool animatingDown = false;
bool animationComplete = false;
unsigned long lastAnimationTime = 0;

void setup() {
  Serial.begin(9600);
  Serial.println("Staircase Automation System Starting...");
  
  // Initialize pins
  pinMode(PIR_BOTTOM_PIN, INPUT);
  pinMode(PIR_TOP_PIN, INPUT);
  pinMode(LED_POWER_PIN, OUTPUT);
  
  // Initialize LED strip
  FastLED.addLeds<LED_TYPE, LED_DATA_PIN, COLOR_ORDER>(leds, NUM_LEDS);
  FastLED.setBrightness(BRIGHTNESS);
  FastLED.clear();
  FastLED.show();
  
  // Power on LEDs
  digitalWrite(LED_POWER_PIN, HIGH);
  
  // Startup animation
  startupAnimation();
  
  Serial.println("System Ready!");
}

void loop() {
  // Read PIR sensors with debouncing
  readPirSensors();
  
  // Handle motion detection logic
  handleMotionLogic();
  
  // Handle LED animations
  handleAnimations();
  
  // Auto turn off after timeout
  handleTimeout();
  
  delay(10); // Small delay for stability
}

void readPirSensors() {
  unsigned long currentTime = millis();

  // Read bottom PIR with enhanced validation
  bool bottomReading = digitalRead(PIR_BOTTOM_PIN);
  if (bottomReading != pirBottomState &&
      (currentTime - lastPirBottomTime) > DEBOUNCE_TIME) {

    if (bottomReading && validateTrigger(currentTime, true)) {
      pirBottomState = bottomReading;
      lastPirBottomTime = currentTime;
      Serial.println("Motion detected at BOTTOM (validated)");
      recordTrigger(currentTime, true);
      detectDirection();
    } else if (!bottomReading) {
      pirBottomState = bottomReading;
      lastPirBottomTime = currentTime;
    }
  }

  // Read top PIR with enhanced validation
  bool topReading = digitalRead(PIR_TOP_PIN);
  if (topReading != pirTopState &&
      (currentTime - lastPirTopTime) > DEBOUNCE_TIME) {

    if (topReading && validateTrigger(currentTime, false)) {
      pirTopState = topReading;
      lastPirTopTime = currentTime;
      Serial.println("Motion detected at TOP (validated)");
      recordTrigger(currentTime, false);
      detectDirection();
    } else if (!topReading) {
      pirTopState = topReading;
      lastPirTopTime = currentTime;
    }
  }
}

void handleMotionLogic() {
  if (pirBottomState || pirTopState) {
    motionDetected = true;
    lastMotionTime = millis();
  }
}

bool validateTrigger(unsigned long currentTime, bool isBottomSensor) {
  // Check minimum interval between triggers
  unsigned long lastTrigger = isBottomSensor ? lastPirBottomTime : lastPirTopTime;
  if ((currentTime - lastTrigger) < minTriggerInterval) {
    Serial.println("Trigger rejected: Too soon after last trigger");
    return false;
  }

  // Don't accept new triggers during animation lock
  if (directionLocked && (currentTime - lastValidDirection) < 2000) {
    Serial.println("Trigger rejected: Direction locked during animation");
    return false;
  }

  // Check for rapid oscillation (false trigger pattern)
  if (isRapidOscillation(currentTime, isBottomSensor)) {
    Serial.println("Trigger rejected: Rapid oscillation detected");
    return false;
  }

  return true;
}

void recordTrigger(unsigned long currentTime, bool isBottomSensor) {
  if (isBottomSensor) {
    lastBottomTrigger = currentTime;
    // Update history
    bottomTriggerHistory[historyIndex % 3] = currentTime;
  } else {
    lastTopTrigger = currentTime;
    // Update history
    topTriggerHistory[historyIndex % 3] = currentTime;
  }
  historyIndex++;
}

bool isRapidOscillation(unsigned long currentTime, bool isBottomSensor) {
  unsigned long* history = isBottomSensor ? bottomTriggerHistory : topTriggerHistory;

  // Check if we have at least 2 previous triggers
  int validTriggers = 0;
  for (int i = 0; i < 3; i++) {
    if (history[i] > 0 && (currentTime - history[i]) < 5000) {
      validTriggers++;
    }
  }

  if (validTriggers >= 2) {
    // Check if triggers are too frequent (oscillation)
    unsigned long avgInterval = 0;
    int intervals = 0;

    for (int i = 0; i < 3; i++) {
      if (history[i] > 0) {
        for (int j = i + 1; j < 3; j++) {
          if (history[j] > 0) {
            avgInterval += abs((long)(history[j] - history[i]));
            intervals++;
          }
        }
      }
    }

    if (intervals > 0) {
      avgInterval /= intervals;
      if (avgInterval < 300) {  // Less than 300ms average = oscillation
        return true;
      }
    }
  }

  return false;
}

void detectDirection() {
  unsigned long currentTime = millis();

  // Skip detection if already animating and direction is locked
  if (directionLocked) {
    return;
  }

  // Check if both sensors have been triggered within the direction window
  if (lastBottomTrigger > 0 && lastTopTrigger > 0) {
    unsigned long timeDiff = abs((long)(lastTopTrigger - lastBottomTrigger));

    if (timeDiff >= minTriggerInterval && timeDiff <= directionWindow) {
      // Determine direction based on which sensor was triggered first
      bool newDirectionUp = (lastBottomTrigger < lastTopTrigger);

      // Require confirmation for direction changes
      if (confirmDirection(newDirectionUp)) {
        directionUp = newDirectionUp;
        directionLocked = true;
        lastValidDirection = currentTime;

        Serial.print("Direction confirmed: ");
        Serial.println(directionUp ? "UPWARD (Bottom → Top)" : "DOWNWARD (Top → Bottom)");

        // Trigger animation based on detected direction
        triggerAnimation(directionUp);

        // Reset triggers after direction is determined
        resetTriggers();
      }
    }
  }

  // Clean up old triggers that are outside the direction window
  if (lastBottomTrigger > 0 && (currentTime - lastBottomTrigger) > maxTriggerInterval) {
    if (lastTopTrigger == 0 && confirmDirection(true)) {
      // Only bottom sensor triggered, assume upward movement
      directionUp = true;
      directionLocked = true;
      lastValidDirection = currentTime;
      Serial.println("Single sensor confirmed: UPWARD (Bottom only)");
      triggerAnimation(true);
    }
    resetTriggers();
  }

  if (lastTopTrigger > 0 && (currentTime - lastTopTrigger) > maxTriggerInterval) {
    if (lastBottomTrigger == 0 && confirmDirection(false)) {
      // Only top sensor triggered, assume downward movement
      directionUp = false;
      directionLocked = true;
      lastValidDirection = currentTime;
      Serial.println("Single sensor confirmed: DOWNWARD (Top only)");
      triggerAnimation(false);
    }
    resetTriggers();
  }
}

bool confirmDirection(bool proposedDirection) {
  // Always confirm first direction
  if (lastValidDirection == 0) {
    confirmationCount = requiredConfirmations;
    return true;
  }

  // Check if enough time has passed since last direction
  unsigned long currentTime = millis();
  if ((currentTime - lastValidDirection) < 10000) {  // 10 seconds cooldown
    Serial.println("Direction change rejected: Too soon after last direction");
    return false;
  }

  // Increment confirmation count
  confirmationCount++;

  if (confirmationCount >= requiredConfirmations) {
    confirmationCount = 0;
    return true;
  }

  Serial.print("Direction needs more confirmation: ");
  Serial.print(confirmationCount);
  Serial.print("/");
  Serial.println(requiredConfirmations);
  return false;
}

void resetTriggers() {
  lastBottomTrigger = 0;
  lastTopTrigger = 0;
  confirmationCount = 0;
}

void triggerAnimation(bool upward) {
  if (!ledsOn || animationComplete) {
    ledsOn = true;
    currentStep = 0;
    animatingUp = upward;
    animatingDown = !upward;
    animationComplete = false;
    lastAnimationTime = millis();
    Serial.println(upward ? "Starting upward animation" : "Starting downward animation");
  } else {
    // If already animating, just reset the timeout
    lastMotionTime = millis();
  }
}

void handleAnimations() {
  unsigned long currentTime = millis();
  
  if ((animatingUp || animatingDown) && 
      (currentTime - lastAnimationTime) > ANIMATION_SPEED) {
    
    if (animatingUp) {
      animateUpward();
    } else if (animatingDown) {
      animateDownward();
    }
    
    lastAnimationTime = currentTime;
  }
}

void animateUpward() {
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  int endLed = min((currentStep + 1) * ledsPerStep, NUM_LEDS);

  // Light up LEDs from bottom to top with smooth wave effect
  for (int i = currentStep * ledsPerStep; i < endLed; i++) {
    leds[i] = CRGB::White;
  }

  // Add trailing fade effect for smoother animation
  if (currentStep > 0) {
    int fadeStart = max(0, (currentStep - 2) * ledsPerStep);
    int fadeEnd = currentStep * ledsPerStep;
    for (int i = fadeStart; i < fadeEnd; i++) {
      leds[i].fadeToBlackBy(64); // Gentle fade
    }
  }

  FastLED.show();
  currentStep++;

  if (currentStep >= FADE_STEPS) {
    animatingUp = false;
    animationComplete = true;
    Serial.println("Upward animation complete");
    // Fill entire strip for final state
    fill_solid(leds, NUM_LEDS, CRGB::White);
    FastLED.show();
  }
}

void animateDownward() {
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  int startLed = max(NUM_LEDS - (currentStep + 1) * ledsPerStep, 0);

  // Light up LEDs from top to bottom with smooth wave effect
  for (int i = NUM_LEDS - currentStep * ledsPerStep - 1; i >= startLed; i--) {
    leds[i] = CRGB::White;
  }

  // Add trailing fade effect for smoother animation
  if (currentStep > 0) {
    int fadeStart = NUM_LEDS - currentStep * ledsPerStep;
    int fadeEnd = min(NUM_LEDS, NUM_LEDS - (currentStep - 2) * ledsPerStep);
    for (int i = fadeStart; i < fadeEnd; i++) {
      leds[i].fadeToBlackBy(64); // Gentle fade
    }
  }

  FastLED.show();
  currentStep++;

  if (currentStep >= FADE_STEPS) {
    animatingDown = false;
    animationComplete = true;
    Serial.println("Downward animation complete");
    // Fill entire strip for final state
    fill_solid(leds, NUM_LEDS, CRGB::White);
    FastLED.show();
  }
}

void handleTimeout() {
  if (motionDetected && ledsOn &&
      (millis() - lastMotionTime) > MOTION_TIMEOUT) {

    if (!pirBottomState && !pirTopState) {
      Serial.println("Timeout reached, turning off LEDs");
      fadeOutLeds();
      ledsOn = false;
      motionDetected = false;
      currentStep = 0;
      animatingUp = false;
      animatingDown = false;
      animationComplete = false;
      directionLocked = false;
      // Reset all triggers and history
      resetTriggers();
      clearTriggerHistory();
    }
  }
}

void clearTriggerHistory() {
  for (int i = 0; i < 3; i++) {
    bottomTriggerHistory[i] = 0;
    topTriggerHistory[i] = 0;
  }
  historyIndex = 0;
  confirmationCount = 0;
}

void fadeOutLeds() {
  for (int brightness = BRIGHTNESS; brightness >= 0; brightness -= 5) {
    FastLED.setBrightness(brightness);
    FastLED.show();
    delay(50);
  }
  
  FastLED.clear();
  FastLED.show();
  FastLED.setBrightness(BRIGHTNESS); // Reset brightness for next time
}

void startupAnimation() {
  Serial.println("Running startup animation...");
  
  // Quick flash to indicate system is ready
  for (int i = 0; i < 3; i++) {
    fill_solid(leds, NUM_LEDS, CRGB::Blue);
    FastLED.show();
    delay(200);
    FastLED.clear();
    FastLED.show();
    delay(200);
  }
}
